import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Package,
  Clock,
  CheckCircle,
  Truck,
  Eye,
  MapPin,
  Phone,
  CreditCard,
  Calendar,
  Filter,
  Search,
  X,
  Navigation,
  ArrowLeft,
  Star,
  Heart,
  Zap,
  TrendingUp,
  Award,
  Sparkles,
  ShoppingBag,
  User,
  ChevronDown,
  ChevronUp,
  RotateCcw,
  RefreshCw,
  AlertCircle,
  Loader2,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { useOrdersStore } from '../../stores/ordersStore';
import { useAuth } from '../../contexts/AuthContext';

type OrderStatus = 'All' | 'Delivered' | 'On the Way' | 'Preparing' | 'Pending' | 'Confirmed' | 'Ready' | 'Cancelled';

const OrdersPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const {
    orders,
    isLoading,
    error,
    pagination,
    fetchOrders,
    refreshOrders,
    setError
  } = useOrdersStore();

  const [selectedFilter, setSelectedFilter] = useState<OrderStatus>('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [ordersPerPage] = useState(10);

  // State for animations and interactions
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [expandedOrders, setExpandedOrders] = useState<Set<string>>(new Set());

  // Smart search debouncing to prevent server overload
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500); // 500ms debounce for search

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // Authentication check and initial data fetch
  useEffect(() => {
    if (!isAuthenticated || !user) {
      navigate('/auth/login');
      return;
    }

    // Fetch orders with pagination on component mount
    fetchOrdersWithPagination();
  }, [isAuthenticated, user, navigate]);

  // Fetch orders when page, filter, or search changes (all server-side now)
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchOrdersWithPagination();
    }
  }, [currentPage, selectedFilter, debouncedSearchQuery, isAuthenticated, user]);

  // Reset to page 1 when search or filter changes
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [debouncedSearchQuery, selectedFilter]);

  // Handle scroll for header animation with debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      // Clear previous timeout
      clearTimeout(timeoutId);

      // Debounce the header compact state change
      timeoutId = setTimeout(() => {
        // Show sliding header after scrolling past the hero section
        setIsHeaderCompact(currentScrollY > 300);
      }, 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Fetch orders with server-side pagination and search
  const fetchOrdersWithPagination = useCallback(async () => {
    try {
      setError(null);

      // Prepare API parameters - backend now supports page, limit, status, and search
      const params: any = {
        page: currentPage,
        limit: ordersPerPage
      };

      // Add status filter if not 'All' - map frontend status to backend status
      if (selectedFilter !== 'All') {
        const statusMap: Record<string, string> = {
          'Pending': 'pending',
          'Confirmed': 'confirmed',
          'Preparing': 'preparing',
          'Ready': 'ready',
          'On the Way': 'out_for_delivery',
          'Delivered': 'delivered',
          'Cancelled': 'cancelled'
        };
        params.status = statusMap[selectedFilter] || selectedFilter.toLowerCase();
      }

      // Add search parameter if exists (now supported by backend)
      if (debouncedSearchQuery.trim()) {
        const searchTerm = debouncedSearchQuery.trim();

        // Client-side validation
        if (searchTerm.length > 100) {
          setError('Search term too long (max 100 characters)');
          return;
        }

        if (searchTerm.length >= 2) {
          params.search = searchTerm;
        }
      }

      await fetchOrders(true, params);
    } catch (error) {
      console.error('Error fetching orders:', error);
      setError('Failed to fetch orders. Please try again.');
    }
  }, [currentPage, ordersPerPage, selectedFilter, debouncedSearchQuery, fetchOrders, setError]);

  // Handle refresh functionality
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchOrdersWithPagination();
    } catch (error) {
      console.error('Error refreshing orders:', error);
      setError('Failed to refresh orders. Please try again.');
    } finally {
      setRefreshing(false);
    }
  }, [fetchOrdersWithPagination, setError]);

  const filters: { key: OrderStatus; label: string; color: string }[] = [
    { key: 'All', label: 'All Orders', color: 'bg-gray-100 text-gray-700' },
    { key: 'Delivered', label: 'Delivered', color: 'bg-green-100 text-green-700' },
    { key: 'On the Way', label: 'On the Way', color: 'bg-orange-100 text-orange-700' },
    { key: 'Preparing', label: 'Preparing', color: 'bg-blue-100 text-blue-700' },
    { key: 'Pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-700' },
    { key: 'Confirmed', label: 'Confirmed', color: 'bg-purple-100 text-purple-700' },
    { key: 'Ready', label: 'Ready', color: 'bg-indigo-100 text-indigo-700' },
    { key: 'Cancelled', label: 'Cancelled', color: 'bg-red-100 text-red-700' }
  ];

  // Server-side pagination and search - everything handled by backend now
  const isSearchActive = debouncedSearchQuery.trim().length > 0;

  // All orders are already filtered and paginated by backend
  const paginatedOrders = orders;

  // Get pagination info from backend response
  let totalOrders, totalPages, startIndex, endIndex;

  if (pagination && pagination.totalOrders !== undefined) {
    // Use server pagination metadata
    totalOrders = pagination.totalOrders;
    totalPages = pagination.totalPages || Math.ceil(totalOrders / ordersPerPage);
    startIndex = (currentPage - 1) * ordersPerPage;
    endIndex = Math.min(startIndex + ordersPerPage, totalOrders);
  } else {
    // Fallback: estimate based on current data
    totalOrders = orders.length;
    totalPages = orders.length === ordersPerPage ? Math.max(currentPage, 1) : currentPage;
    startIndex = (currentPage - 1) * ordersPerPage;
    endIndex = startIndex + orders.length;
  }

  // Reset to first page when filters change (but not when currentPage changes)
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedFilter]);



  // Pagination handlers with scroll to top (server-side pagination)
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const canGoNext = pagination?.hasNextPage || (currentPage < totalPages);
  const canGoPrev = pagination?.hasPrevPage || (currentPage > 1);

  const goToNextPage = useCallback(() => {
    if (canGoNext) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      scrollToTop();

      // For server-side pagination, the useEffect will trigger fetchOrdersWithPagination
      // For client-side (search), no API call needed
    }
  }, [canGoNext, currentPage]);

  const goToPreviousPage = useCallback(() => {
    if (canGoPrev) {
      const prevPage = currentPage - 1;
      setCurrentPage(prevPage);
      scrollToTop();

      // For server-side pagination, the useEffect will trigger fetchOrdersWithPagination
      // For client-side (search), no API call needed
    }
  }, [canGoPrev, currentPage]);

  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page);
      scrollToTop();

      // For server-side pagination, the useEffect will trigger fetchOrdersWithPagination
      // For client-side (search), no API call needed
    }
  }, [totalPages, currentPage]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Delivered': return <CheckCircle size={16} className="text-green-600" />;
      case 'On the Way': return <Truck size={16} className="text-orange-600" />;
      case 'Preparing': return <Clock size={16} className="text-blue-600" />;
      case 'Confirmed': return <CheckCircle size={16} className="text-purple-600" />;
      case 'Ready': return <Package size={16} className="text-indigo-600" />;
      case 'Cancelled': return <X size={16} className="text-red-600" />;
      default: return <Package size={16} className="text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered': return 'text-green-600 bg-green-50 border-green-200';
      case 'On the Way': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'Preparing': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'Confirmed': return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'Ready': return 'text-indigo-600 bg-indigo-50 border-indigo-200';
      case 'Cancelled': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  // Show loading state
  if (isLoading && orders.length === 0) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
        </div>

        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white/10 backdrop-blur-xl rounded-3xl p-12 text-center shadow-2xl border border-white/20"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="mb-6"
            >
              <Loader2 size={48} className="text-white mx-auto" />
            </motion.div>
            <h3 className="text-2xl font-bold text-white mb-4">Loading Your Orders</h3>
            <p className="text-white/70">Please wait while we fetch your order history...</p>
          </motion.div>
        </div>
      </div>
    );
  }

  // Show authentication error
  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900" />
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white/10 backdrop-blur-xl rounded-3xl p-12 text-center shadow-2xl border border-white/20"
          >
            <AlertCircle size={48} className="text-red-400 mx-auto mb-6" />
            <h3 className="text-2xl font-bold text-white mb-4">Authentication Required</h3>
            <p className="text-white/70 mb-8">Please log in to view your orders.</p>
            <motion.button
              onClick={() => navigate('/auth/login')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-2xl font-bold"
            >
              Go to Login
            </motion.button>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-purple-500/30 to-pink-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-br from-indigo-500/20 to-cyan-500/20 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.1, 1, 1.1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 9,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
          className="absolute top-1/4 right-1/4 w-64 h-64 bg-gradient-to-br from-emerald-500/20 to-blue-500/20 rounded-full blur-3xl"
        />
      </div>

      {/* Sliding Header with Scroll Animation - Only appears when scrolling */}
      <motion.div
        className="fixed left-0 right-0 z-50"
        initial={{ y: -100, opacity: 0 }}
        animate={{
          y: isHeaderCompact ? 0 : -100,
          opacity: isHeaderCompact ? 1 : 0,
        }}
        transition={{
          duration: 0.4,
          ease: "easeInOut",
          type: "spring",
          stiffness: 300,
          damping: 30
        }}
        style={{
          top: 0,
          pointerEvents: isHeaderCompact ? "auto" : "none"
        }}
      >
        <div className="bg-slate-900/95 backdrop-blur-xl border-b border-white/10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between py-4">
              {/* Left side - Logo and title */}
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl"
                >
                  <ShoppingBag className="w-6 h-6 text-white" />
                </motion.div>
                <div>
                  <h1 className="font-bold text-xl bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
                    My Orders
                  </h1>
                  <p className="text-white/60 text-sm">
                    {totalOrders > 0
                      ? `Page ${currentPage} of ${totalPages} • ${totalOrders} orders`
                      : "Track and manage your orders"
                    }
                  </p>
                </div>
              </div>

              {/* Right side - Compact search bar and refresh button */}
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-3 bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 px-4 py-2 min-w-[300px]">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                    className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg"
                  >
                    <Search className="w-4 h-4 text-white" />
                  </motion.div>
                  <input
                    type="text"
                    placeholder="Search orders..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    maxLength={100}
                    className="flex-1 bg-transparent text-white placeholder-white/60 outline-none text-sm font-medium"
                  />
                  {searchQuery.trim() && (
                    <motion.button
                      onClick={() => setSearchQuery('')}
                      whileHover={{
                        scale: 1.1,
                        transition: { duration: 0.2, ease: "easeOut" }
                      }}
                      whileTap={{
                        scale: 0.9,
                        transition: { duration: 0.1, ease: "easeInOut" }
                      }}
                      className="p-1 hover:bg-white/10 rounded-lg transition-colors"
                    >
                      <X className="w-4 h-4 text-white/70" />
                    </motion.button>
                  )}
                </div>

                {/* Refresh button */}
                <motion.button
                  onClick={handleRefresh}
                  disabled={refreshing}
                  whileHover={{
                    scale: 1.05,
                    transition: { duration: 0.2, ease: "easeOut" }
                  }}
                  whileTap={{
                    scale: 0.95,
                    transition: { duration: 0.1, ease: "easeInOut" }
                  }}
                  className="p-3 bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 hover:bg-white/20 transition-colors disabled:opacity-50"
                >
                  <motion.div
                    animate={refreshing ? { rotate: 360 } : {}}
                    transition={refreshing ? { duration: 1, repeat: Infinity, ease: "linear" } : {}}
                  >
                    <RefreshCw className="w-4 h-4 text-white" />
                  </motion.div>
                </motion.button>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Content Section with proper spacing */}
      <div className="relative z-10 pb-20 pt-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 space-y-8">

          {/* Premium Search Bar - Hidden when header is compact */}
          <AnimatePresence>
            {!isHeaderCompact && (
              <motion.div
                initial={{ opacity: 0, y: 50, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -20, scale: 0.95 }}
                transition={{
                  duration: 0.8,
                  delay: 0.1,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  type: "tween"
                }}
                className="mb-8"
              >
            <motion.div
              whileHover={{
                scale: 1.02,
                y: -2,
                transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
              }}
              className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-6 shadow-2xl"
            >
              <div className="flex items-center gap-4">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                  className="p-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl"
                >
                  <Search className="w-6 h-6 text-white" />
                </motion.div>
                <input
                  type="text"
                  placeholder="Search orders, restaurants, or items..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  maxLength={100}
                  className="flex-1 bg-transparent text-white placeholder-white/60 outline-none text-lg font-medium"
                />
                {searchQuery.trim() && (
                  <motion.button
                    onClick={() => setSearchQuery('')}
                    whileHover={{
                      scale: 1.1,
                      transition: { duration: 0.2, ease: "easeOut" }
                    }}
                    whileTap={{
                      scale: 0.9,
                      transition: { duration: 0.1, ease: "easeInOut" }
                    }}
                    className="p-2 hover:bg-white/10 rounded-xl transition-colors backdrop-blur-sm border border-white/10"
                  >
                    <X className="w-5 h-5 text-white/70" />
                  </motion.button>
                )}

                {/* Refresh button in main search bar */}
                <motion.button
                  onClick={handleRefresh}
                  disabled={refreshing}
                  whileHover={{
                    scale: 1.05,
                    transition: { duration: 0.2, ease: "easeOut" }
                  }}
                  whileTap={{
                    scale: 0.95,
                    transition: { duration: 0.1, ease: "easeInOut" }
                  }}
                  className="p-2 hover:bg-white/10 rounded-xl transition-colors backdrop-blur-sm border border-white/10 disabled:opacity-50"
                >
                  <motion.div
                    animate={refreshing ? { rotate: 360 } : {}}
                    transition={refreshing ? { duration: 1, repeat: Infinity, ease: "linear" } : {}}
                  >
                    <RefreshCw className="w-5 h-5 text-white/70" />
                  </motion.div>
                </motion.button>
              </div>
            </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Premium Filter Bar */}
          <motion.div
            initial={{ opacity: 0, y: 50, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{
              duration: 0.8,
              delay: 0.2,
              ease: [0.25, 0.46, 0.45, 0.94],
              type: "tween"
            }}
            className="mb-8"
          >
            <motion.div
              whileHover={{
                scale: 1.02,
                y: -2,
                transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
              }}
              className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 shadow-2xl"
            >
              <div className="flex items-center gap-4 mb-6">
                <motion.div
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl"
                >
                  <Filter className="w-6 h-6 text-white" />
                </motion.div>
                <h3 className="text-xl font-bold text-white">Filter Orders</h3>
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
                  className="ml-auto p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl"
                >
                  <Sparkles className="w-5 h-5 text-white" />
                </motion.div>
              </div>
              <div className="flex flex-wrap gap-3">
                {filters.map((filter, index) => (
                  <motion.button
                    key={filter.key}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                      delay: index * 0.1,
                      duration: 0.5,
                      ease: [0.25, 0.46, 0.45, 0.94]
                    }}
                    onClick={() => setSelectedFilter(filter.key)}
                    whileHover={{
                      scale: 1.05,
                      y: -2,
                      transition: { duration: 0.2, ease: "easeOut" }
                    }}
                    whileTap={{
                      scale: 0.95,
                      transition: { duration: 0.1, ease: "easeInOut" }
                    }}
                    className={`px-6 py-3 rounded-2xl text-sm font-bold shadow-lg transition-all duration-300 ${
                      selectedFilter === filter.key
                        ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-purple-500/25'
                        : 'bg-white/10 text-white border border-white/20 hover:bg-white/20'
                    }`}
                  >
                    {filter.label}
                  </motion.button>
                ))}
              </div>
            </motion.div>
          </motion.div>

          {/* Pagination Controls */}
          {totalOrders > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.6,
                delay: 0.25,
                ease: [0.25, 0.46, 0.45, 0.94],
                type: "tween"
              }}
              className="mb-8"
            >
              <motion.div
                whileHover={{
                  scale: 1.02,
                  y: -2,
                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                }}
                className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-6 shadow-2xl"
              >
                <div className="flex items-center justify-between">
                  {/* Results Info */}
                  <div className="flex items-center gap-4">
                    <motion.div
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                      className="p-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl"
                    >
                      <Package className="w-6 h-6 text-white" />
                    </motion.div>
                    <div>
                      <h3 className="text-lg font-bold text-white">
                        Showing {startIndex + 1}-{endIndex} of {
                          pagination && pagination.totalOrders !== undefined
                            ? totalOrders
                            : `${totalOrders}${orders.length === ordersPerPage ? '+' : ''}`
                        } orders
                        {isSearchActive && (
                          <span className="ml-2 text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded-lg">
                            Search Results
                          </span>
                        )}
                      </h3>
                      <p className="text-white/60 text-sm">
                        Page {currentPage} of {
                          pagination && pagination.totalPages > 0
                            ? totalPages
                            : `${totalPages}${orders.length === ordersPerPage ? '+' : ''}`
                        }
                      </p>
                    </div>
                  </div>

                  {/* Pagination Controls */}
                  <div className="flex items-center gap-2">
                    {/* Previous Button */}
                    <motion.button
                      onClick={goToPreviousPage}
                      disabled={!canGoPrev}
                      whileHover={canGoPrev ? {
                        scale: 1.05,
                        transition: { duration: 0.2, ease: "easeOut" }
                      } : {}}
                      whileTap={canGoPrev ? {
                        scale: 0.95,
                        transition: { duration: 0.1, ease: "easeInOut" }
                      } : {}}
                      className={`p-3 rounded-2xl font-bold transition-all duration-300 flex items-center gap-2 ${
                        !canGoPrev
                          ? 'bg-white/5 text-white/30 cursor-not-allowed'
                          : 'bg-white/10 text-white border border-white/20 hover:bg-white/20'
                      }`}
                    >
                      <ChevronLeft className="w-5 h-5" />
                      Previous
                    </motion.button>

                    {/* Page Numbers */}
                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        // Ensure pageNum is valid
                        if (pageNum < 1 || pageNum > totalPages) {
                          return null;
                        }

                        const isCurrentPage = currentPage === pageNum;

                        return (
                          <motion.button
                            key={`page-${pageNum}`}
                            onClick={() => goToPage(pageNum)}
                            disabled={isCurrentPage}
                            whileHover={!isCurrentPage ? {
                              scale: 1.1,
                              transition: { duration: 0.2, ease: "easeOut" }
                            } : {}}
                            whileTap={!isCurrentPage ? {
                              scale: 0.9,
                              transition: { duration: 0.1, ease: "easeInOut" }
                            } : {}}
                            className={`w-10 h-10 rounded-xl font-bold transition-all duration-300 ${
                              isCurrentPage
                                ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg cursor-default'
                                : 'bg-white/10 text-white border border-white/20 hover:bg-white/20 cursor-pointer'
                            }`}
                          >
                            {pageNum}
                          </motion.button>
                        );
                      }).filter(Boolean)}
                    </div>

                    {/* Next Button */}
                    <motion.button
                      onClick={goToNextPage}
                      disabled={!canGoNext}
                      whileHover={canGoNext ? {
                        scale: 1.05,
                        transition: { duration: 0.2, ease: "easeOut" }
                      } : {}}
                      whileTap={canGoNext ? {
                        scale: 0.95,
                        transition: { duration: 0.1, ease: "easeInOut" }
                      } : {}}
                      className={`p-3 rounded-2xl font-bold transition-all duration-300 flex items-center gap-2 ${
                        !canGoNext
                          ? 'bg-white/5 text-white/30 cursor-not-allowed'
                          : 'bg-white/10 text-white border border-white/20 hover:bg-white/20'
                      }`}
                    >
                      Next
                      <ChevronRight className="w-5 h-5" />
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-500/10 backdrop-blur-xl rounded-3xl p-6 border border-red-500/20 mb-6"
          >
            <div className="flex items-center gap-4">
              <AlertCircle className="w-6 h-6 text-red-400" />
              <div>
                <h3 className="text-red-400 font-bold text-lg">Error Loading Orders</h3>
                <p className="text-red-300">{error}</p>
              </div>
              <motion.button
                onClick={handleRefresh}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="ml-auto bg-red-500/20 text-red-400 px-4 py-2 rounded-xl font-medium hover:bg-red-500/30 transition-colors"
              >
                Retry
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* Orders List */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="space-y-6"
        >
          {isLoading && orders.length > 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-4"
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="inline-block"
              >
                <Loader2 size={24} className="text-white/60" />
              </motion.div>
              <p className="text-white/60 mt-2">Refreshing orders...</p>
            </motion.div>
          )}

          {paginatedOrders.length === 0 && !isLoading ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.8, y: 50 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              transition={{
                duration: 0.8,
                ease: [0.25, 0.46, 0.45, 0.94],
                type: "tween"
              }}
              className="bg-white/10 backdrop-blur-xl rounded-3xl p-16 text-center shadow-2xl border border-white/20"
            >
              <motion.div
                animate={{
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="mb-8"
              >
                <div className="relative">
                  <Package size={80} className="text-white/60 mx-auto" />
                  <motion.div
                    animate={{ scale: [1, 1.2, 1], opacity: [0.5, 1, 0.5] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"
                  />
                </div>
              </motion.div>
              <h3 className="text-3xl font-bold text-white mb-4 bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
                No Orders Found
              </h3>
              <p className="text-white/70 text-lg mb-8">
                {debouncedSearchQuery.trim()
                  ? `No orders match "${debouncedSearchQuery}"`
                  : selectedFilter === 'All'
                    ? totalOrders === 0
                      ? "You haven't placed any orders yet"
                      : `No orders on page ${currentPage}`
                    : totalOrders === 0
                      ? `No ${selectedFilter.toLowerCase()} orders found`
                      : `No ${selectedFilter.toLowerCase()} orders on page ${currentPage}`
                }
              </p>
              <motion.button
                onClick={() => navigate('/customer/home')}
                whileHover={{
                  scale: 1.05,
                  y: -2,
                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                }}
                whileTap={{
                  scale: 0.95,
                  transition: { duration: 0.15, ease: "easeInOut" }
                }}
                className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-2xl font-bold shadow-2xl hover:shadow-purple-500/25 flex items-center gap-3 mx-auto"
                style={{
                  transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
                }}
              >
                <ShoppingBag className="w-5 h-5" />
                Start Shopping
              </motion.button>
            </motion.div>
          ) : (
            <AnimatePresence mode="wait" key={selectedFilter}>
              <motion.div
                key={selectedFilter}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{
                  duration: 0.5,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  type: "tween"
                }}
                className="space-y-6"
              >
                {paginatedOrders.map((order, index) => {
                  const isExpanded = expandedOrders.has(order.id);
                  return (
                    <motion.div
                      key={order.id}
                      initial={{ opacity: 0, y: 30, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      transition={{
                        duration: 0.6,
                        delay: index * 0.1,
                        ease: [0.25, 0.46, 0.45, 0.94],
                        type: "tween"
                      }}
                      whileHover={{
                        y: -8,
                        scale: 1.02,
                        transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                      }}
                      className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden"
                    >
                    {/* Premium Order Header */}
                    <div className="p-8 border-b border-white/10">
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center gap-6">
                          <motion.div
                            whileHover={{
                              rotate: 360,
                              transition: { duration: 0.5, ease: "easeInOut" }
                            }}
                            className="w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center shadow-2xl"
                          >
                            <Package className="text-white" size={28} />
                          </motion.div>
                          <div>
                            <h3 className="font-bold text-2xl text-white mb-1">#{order.id.slice(-8)}</h3>
                            <div className="flex items-center gap-2">
                              <motion.div
                                animate={{ scale: [1, 1.1, 1] }}
                                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                                className="w-2 h-2 bg-gradient-to-r from-green-400 to-blue-400 rounded-full"
                              />
                              <p className="text-white/80 text-lg font-medium">{order.supplier.name}</p>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <motion.div
                            whileHover={{
                              scale: 1.05,
                              transition: { duration: 0.2, ease: "easeOut" }
                            }}
                            className="inline-flex items-center gap-3 px-6 py-3 rounded-2xl text-sm font-bold border bg-white/10 border-white/20 text-white shadow-lg backdrop-blur-sm mb-2"
                          >
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                            >
                              {getStatusIcon(order.status)}
                            </motion.div>
                            {order.status}
                          </motion.div>
                          <p className="text-white/60 text-sm">
                            {formatDate(order.createdAt).date} • {formatDate(order.createdAt).time}
                          </p>
                        </div>
                      </div>

                      {/* Expand/Collapse Button */}
                      <motion.button
                        onClick={() => {
                          const newExpanded = new Set(expandedOrders);
                          if (isExpanded) {
                            newExpanded.delete(order.id);
                          } else {
                            newExpanded.add(order.id);
                          }
                          setExpandedOrders(newExpanded);
                        }}
                        whileHover={{
                          scale: 1.05,
                          transition: { duration: 0.2, ease: "easeOut" }
                        }}
                        whileTap={{
                          scale: 0.95,
                          transition: { duration: 0.1, ease: "easeInOut" }
                        }}
                        className="w-full flex items-center justify-center gap-3 py-3 bg-white/5 rounded-2xl border border-white/10 text-white font-medium hover:bg-white/10 transition-colors"
                      >
                        <span>{isExpanded ? 'Hide Details' : 'Show Details'}</span>
                        <motion.div
                          animate={{ rotate: isExpanded ? 180 : 0 }}
                          transition={{ duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }}
                        >
                          <ChevronDown className="w-5 h-5" />
                        </motion.div>
                      </motion.button>
                    </div>

                    {/* Collapsible Order Details */}
                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{
                            duration: 0.5,
                            ease: [0.25, 0.46, 0.45, 0.94],
                            type: "tween"
                          }}
                          className="overflow-hidden"
                        >
                          <div className="p-8 space-y-8">
                            {/* Order Items Section */}
                            <motion.div
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.1, duration: 0.5 }}
                              className="bg-white/5 rounded-2xl p-6 border border-white/10"
                            >
                              <div className="flex items-center gap-3 mb-6">
                                <motion.div
                                  animate={{ rotate: [0, 360] }}
                                  transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                                  className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl"
                                >
                                  <Package className="w-5 h-5 text-white" />
                                </motion.div>
                                <h4 className="font-bold text-white text-lg">Order Items</h4>
                              </div>
                              <div className="space-y-3">
                                {order.items.map((item, idx) => (
                                  <motion.div
                                    key={idx}
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: idx * 0.1, duration: 0.4 }}
                                    whileHover={{
                                      scale: 1.02,
                                      x: 4,
                                      transition: { duration: 0.2, ease: "easeOut" }
                                    }}
                                    className="flex justify-between items-center py-4 px-4 bg-white/5 rounded-xl border border-white/10"
                                  >
                                    <div className="flex items-center gap-3">
                                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                                        {item.qty}
                                      </div>
                                      <span className="text-white font-medium">
                                        {item.product.name}
                                      </span>
                                    </div>
                                    <span className="font-bold text-white text-lg">
                                      ₪{(item.finalPrice * item.qty).toFixed(2)}
                                    </span>
                                  </motion.div>
                                ))}
                              </div>
                            </motion.div>

                            {/* Delivery Info Section */}
                            <motion.div
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.2, duration: 0.5 }}
                              className="bg-white/5 rounded-2xl p-6 border border-white/10"
                            >
                              <div className="flex items-center gap-3 mb-6">
                                <motion.div
                                  animate={{ y: [0, -5, 0] }}
                                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                                  className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl"
                                >
                                  <MapPin className="w-5 h-5 text-white" />
                                </motion.div>
                                <h4 className="font-bold text-white text-lg">Delivery Details</h4>
                              </div>
                              <div className="space-y-4">
                                <motion.div
                                  whileHover={{
                                    scale: 1.02,
                                    x: 4,
                                    transition: { duration: 0.2, ease: "easeOut" }
                                  }}
                                  className="flex items-center gap-4 p-4 bg-white/5 rounded-xl border border-white/10"
                                >
                                  <div className="p-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg">
                                    <MapPin className="w-4 h-4 text-white" />
                                  </div>
                                  <span className="text-white font-medium">{order.address}</span>
                                </motion.div>
                                <motion.div
                                  whileHover={{
                                    scale: 1.02,
                                    x: 4,
                                    transition: { duration: 0.2, ease: "easeOut" }
                                  }}
                                  className="flex items-center gap-4 p-4 bg-white/5 rounded-xl border border-white/10"
                                >
                                  <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
                                    <Phone className="w-4 h-4 text-white" />
                                  </div>
                                  <span className="text-white font-medium">{order.phone}</span>
                                </motion.div>
                                <motion.div
                                  whileHover={{
                                    scale: 1.02,
                                    x: 4,
                                    transition: { duration: 0.2, ease: "easeOut" }
                                  }}
                                  className="flex items-center gap-4 p-4 bg-white/5 rounded-xl border border-white/10"
                                >
                                  <div className="p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg">
                                    <Clock className="w-4 h-4 text-white" />
                                  </div>
                                  <span className="text-white font-medium">Est. {order.estimatedTime}</span>
                                </motion.div>
                              </div>
                            </motion.div>

                            {/* Payment & Driver Info Section */}
                            <div className="grid md:grid-cols-2 gap-6">
                              {/* Payment Info */}
                              <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.3, duration: 0.5 }}
                                className="bg-white/5 rounded-2xl p-6 border border-white/10"
                              >
                                <div className="flex items-center gap-3 mb-6">
                                  <motion.div
                                    animate={{ scale: [1, 1.1, 1] }}
                                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                                    className="p-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl"
                                  >
                                    <CreditCard className="w-5 h-5 text-white" />
                                  </motion.div>
                                  <h4 className="font-bold text-white text-lg">Payment</h4>
                                </div>
                                <div className="space-y-4">
                                  <div className="flex justify-between items-center p-4 bg-white/5 rounded-xl border border-white/10">
                                    <span className="text-white/70">Payment Method:</span>
                                    <span className="font-bold text-white capitalize">{order.paymentMethod}</span>
                                  </div>
                                  <motion.div
                                    whileHover={{
                                      scale: 1.02,
                                      transition: { duration: 0.2, ease: "easeOut" }
                                    }}
                                    className="flex justify-between items-center p-4 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 rounded-xl border border-emerald-400/30"
                                  >
                                    <span className="text-white font-bold text-lg">Total:</span>
                                    <span className="text-white font-bold text-2xl">₪{order.total.toFixed(2)}</span>
                                  </motion.div>
                                </div>
                              </motion.div>

                              {/* Driver Info */}
                              {order.driverName && (
                                <motion.div
                                  initial={{ opacity: 0, y: 20 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ delay: 0.4, duration: 0.5 }}
                                  className="bg-white/5 rounded-2xl p-6 border border-white/10"
                                >
                                  <div className="flex items-center gap-3 mb-6">
                                    <motion.div
                                      animate={{ rotate: [0, 10, -10, 0] }}
                                      transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                                      className="p-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl"
                                    >
                                      <Truck className="w-5 h-5 text-white" />
                                    </motion.div>
                                    <h4 className="font-bold text-white text-lg">Driver</h4>
                                  </div>
                                  <div className="space-y-3">
                                    <div className="flex justify-between items-center p-4 bg-white/5 rounded-xl border border-white/10">
                                      <span className="text-white/70">Name:</span>
                                      <span className="font-bold text-white">{order.driverName}</span>
                                    </div>
                                    <div className="flex justify-between items-center p-4 bg-white/5 rounded-xl border border-white/10">
                                      <span className="text-white/70">Phone:</span>
                                      <span className="font-bold text-white">{order.driverPhone}</span>
                                    </div>
                                  </div>
                                </motion.div>
                              )}
                            </div>

                            {/* Premium Action Buttons */}
                            <motion.div
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.5, duration: 0.5 }}
                              className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t border-white/10"
                            >
                              <motion.button
                                onClick={() => navigate(`/customer/order-details?orderId=${order.id}`)}
                                whileHover={{
                                  scale: 1.05,
                                  y: -2,
                                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                                }}
                                whileTap={{
                                  scale: 0.95,
                                  transition: { duration: 0.15, ease: "easeInOut" }
                                }}
                                className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-6 rounded-2xl font-bold shadow-2xl hover:shadow-blue-500/25 flex items-center justify-center gap-3"
                                style={{
                                  transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
                                }}
                              >
                                <Eye className="w-5 h-5" />
                                View Details
                              </motion.button>

                              {(order.status === 'Preparing' || order.status === 'On the Way') && (
                                <motion.button
                                  onClick={() => navigate(`/customer/order-tracking?orderId=${order.id}`)}
                                  whileHover={{
                                    scale: 1.05,
                                    y: -2,
                                    transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                                  }}
                                  whileTap={{
                                    scale: 0.95,
                                    transition: { duration: 0.15, ease: "easeInOut" }
                                  }}
                                  className="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-4 px-6 rounded-2xl font-bold shadow-2xl hover:shadow-green-500/25 flex items-center justify-center gap-3"
                                  style={{
                                    transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
                                  }}
                                >
                                  <motion.div
                                    animate={{ rotate: 360 }}
                                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                                  >
                                    <Navigation className="w-5 h-5" />
                                  </motion.div>
                                  Track Live
                                </motion.button>
                              )}

                              <motion.button
                                onClick={() => navigate(`/customer/supplier-details?supplierId=${order.supplier.id}`)}
                                whileHover={{
                                  scale: 1.05,
                                  y: -2,
                                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                                }}
                                whileTap={{
                                  scale: 0.95,
                                  transition: { duration: 0.15, ease: "easeInOut" }
                                }}
                                className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-4 px-6 rounded-2xl font-bold shadow-2xl hover:shadow-purple-500/25 flex items-center justify-center gap-3"
                                style={{
                                  transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
                                }}
                              >
                                <RotateCcw className="w-5 h-5" />
                                Reorder
                              </motion.button>
                            </motion.div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                );
              })}
              </motion.div>
            </AnimatePresence>
          )}
        </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default OrdersPage;